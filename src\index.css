/* Import elegant, readable fonts */
@import url('https://fonts.googleapis.com/css2?family=Source+Sans+3:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles for typography */
@layer base {

  html,
  body {
    font-family: 'Source Sans 3', system-ui, -apple-system, sans-serif;
    @apply text-black;
    transition: background-color 0.5s, color 0.5s, border-color 0.5s;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: 'Montserrat', system-ui, -apple-system, sans-serif;
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-4xl md:text-5xl mb-6;
  }

  h2 {
    @apply text-3xl md:text-4xl mb-5;
  }

  h3 {
    @apply text-2xl md:text-3xl mb-4;
  }

  p {
    @apply text-base md:text-lg leading-relaxed mb-4;
  }
}

/* Custom components */
@layer components {
  .container {
    @apply px-4 mx-auto max-w-7xl;
  }
}

.text-shadow {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hover-input {
  padding: 10px;
  transition: border-color 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
}

.hover-input:hover {
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.5);
  /* Add shadow on hover */
}

.testimonial-card {
  perspective: 1000px;
  height: 400px;
  position: relative;
  overflow: hidden;
  margin-bottom: 2rem;
}

.testimonial-inner {
  position: absolute;
  width: 100%;
  height: 100%;
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.testimonial-card:hover .testimonial-inner {
  transform: rotateY(180deg);
}

.testimonial-front,
.testimonial-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  padding: 20px;
  /* background: white; */
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.testimonial-front {
  z-index: 2;
  transform: rotateY(0deg);
}

.testimonial-back {
  transform: rotateY(180deg);
}

.faq-page {
  padding: 20px;
}

.faq-item {
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.faq-item:hover {
  background-color: rgba(255, 165, 0, 0.1);
  /* Light orange background on hover */
}

.faq-item h2 {
  margin-bottom: 10px;
}

.faq-item p {
  margin-top: 10px;
}

.terms {
  color: blue;
}

.btn-shadow {
  transition: all 0.3s;
}

.btn-shadow:hover {
  text-shadow: 4px 4px 4px rgba(0, 0, 0, 0.5);
  transform: scale(1.05);
}


#sticky-header {

  position: sticky;
  top: 0;
  background: white;
  padding: 10px 0;
  text-align: center;
  z-index: 1000;
  /* Ensures it stays above other elements */
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
  width: 100%;
  left: 0;
  /* align to left */
  margin: 0;
  /* no side margins */
}


/* Custom scrollbar */
::-webkit-scrollbar {
  width: 15px;
  /* Set the width of the scrollbar */
}

::-webkit-scrollbar-track {
  background-color: #f0f0f0;
  /* Light background for the track */
  border-radius: 20px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #FFFF8F, lightcoral);
  /* Linear gradient from light yellow to light red */
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #FFFF8F, lightcoral);
  /* Keep the gradient on hover */
}

/*Header Styles*/

.shimmer-hover {
  position: relative;
  z-index: 0;
  overflow: hidden;
}

.shimmer-hover::before {
  content: "";
  position: absolute;
  top: 0;
  left: -75%;
  width: 50%;
  height: 100%;
  background: linear-gradient(120deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(234, 88, 12, 0.4) 50%,
      /* orange-600 shimmer for light mode */
      rgba(255, 255, 255, 0) 100%);
  transform: skewX(-20deg);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.dark .shimmer-hover::before {
  background: linear-gradient(120deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.4) 50%,
      /* white shimmer for dark mode */
      rgba(255, 255, 255, 0) 100%);
}

.shimmer-hover:hover::before,
.shimmer-hover-active::before {
  opacity: 1;
  animation: shimmerMoveContinuous 1s linear infinite;
}

@keyframes shimmerMoveContinuous {
  0% {
    left: -75%;
  }

  100% {
    left: 125%;
  }
}

@layer utilities {
  @keyframes slide-in {
    from {
      opacity: 0;
      transform: translate(-50%, -55%) scale(0.95);
    }

    to {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
  }

  .animate-slide-in {
    animation: slide-in 0.3s ease-out;
  }
}

/* Flip animation utilities */
.perspective {
  perspective: 1000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

.my-rotate-y-180 {
  transform: rotateY(180deg);
}

.group-hover\:my-rotate-y-180:hover {
  transform: rotateY(180deg);
}

.backface-hidden {
  backface-visibility: hidden;

}




.flip-card {
  perspective: 1000px;
}

.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 320px;
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.flip-card:hover .flip-card-inner {
  transform: rotateY(180deg);
}

.flip-card-front,
.flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 0.75rem; /* Tailwind rounded-lg */
}

.flip-card-back {
  transform: rotateY(180deg);
}

