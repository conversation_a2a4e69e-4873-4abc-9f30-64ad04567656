{"name": "techieblog", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "start": "vite --host", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@mui/icons-material": "^6.3.1", "@mui/material": "^6.1.7", "@reduxjs/toolkit": "^2.3.0", "@tinymce/tinymce-react": "^5.1.1", "@uiw/react-md-editor": "^4.0.5", "appwrite": "^16.0.2", "axios": "^1.7.9", "caniuse-lite": "^1.0.30001723", "cors": "^2.8.5", "data-fns": "^1.1.0", "date-fns": "^4.1.0", "emailjs-com": "^3.2.0", "express": "^5.1.0", "framer-motion": "^12.4.2", "html-react-parser": "^5.1.18", "lucide-react": "^0.469.0", "react": "^18.3.1", "react-confetti": "^6.2.2", "react-dom": "^18.3.1", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.53.1", "react-icons": "^5.5.0", "react-redux": "^9.1.2", "react-responsive-carousel": "^3.2.23", "react-router-dom": "^6.28.0", "react-snowfall": "^2.2.0", "react-spinners": "^0.15.0", "react-sweetalert2": "^0.6.0", "react-toastify": "^11.0.5", "react-type-animation": "^3.2.0", "react-use": "^17.6.0", "redux-persist": "^6.0.0", "sonner": "^1.7.4", "sooner": "^1.1.1", "tinymce": "^7.5.1", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "vite": "^6.3.5"}}