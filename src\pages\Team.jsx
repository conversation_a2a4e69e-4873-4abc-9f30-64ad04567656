// src/pages/Team.jsx

import { Card, CardContent } from "@/components/ui/card";
import { Github, Linkedin, Twitter } from "lucide-react";

const teamMembers = [
  {
    name: "<PERSON>",
    role: "Full Stack Developer",
    image: "https://via.placeholder.com/150", // replace with real image
    socials: {
      linkedin: "#",
      github: "#",
      twitter: "#",
    },
  },
  {
    name: "ABC",
    role: "UI/UX Designer",
    image: "https://via.placeholder.com/150",
    socials: {
      linkedin: "#",
      github: "#",
      twitter: "#",
    },
  },
  {
    name: "<PERSON>",
    role: "Backend Developer",
    image: "https://via.placeholder.com/150",
    socials: {
      linkedin: "#",
      github: "#",
      twitter: "#",
    },
  },
];

export default function Team() {
  return (
    <section className="py-16 bg-gradient-to-b from-white to-slate-50 dark:from-slate-900 dark:to-slate-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          Meet Our Team
        </h2>
        <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-12">
          The passionate people working together to bring you the best insights in tech.
        </p>

        {/* Team Members Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {teamMembers.map((member, index) => (
            <Card
              key={index}
              className="rounded-2xl shadow-md hover:shadow-xl transition-all"
            >
              <CardContent className="p-6 flex flex-col items-center">
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-28 h-28 rounded-full object-cover border-4 border-orange-500 mb-4"
                />
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                  {member.name}
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  {member.role}
                </p>

                {/* Social Links */}
                <div className="flex space-x-4">
                  <a
                    href={member.socials.linkedin}
                    className="text-gray-600 dark:text-gray-300 hover:text-orange-500"
                  >
                    <Linkedin size={20} />
                  </a>
                  <a
                    href={member.socials.github}
                    className="text-gray-600 dark:text-gray-300 hover:text-orange-500"
                  >
                    <Github size={20} />
                  </a>
                  <a
                    href={member.socials.twitter}
                    className="text-gray-600 dark:text-gray-300 hover:text-orange-500"
                  >
                    <Twitter size={20} />
                  </a>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
