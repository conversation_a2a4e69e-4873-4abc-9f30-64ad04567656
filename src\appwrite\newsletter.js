import { Client, ID, Databases, Query } from 'appwrite';
import conf from '../conf/conf';
import emailjs from '@emailjs/browser';

export class NewsletterService {
    client = new Client();
    databases;

    constructor() {
        this.client
            .setEndpoint(conf.appwriteUrl)
            .setProject(conf.appwriteProjectId);

        this.databases = new Databases(this.client);
    }

    async subscribeToNewsletter(email) {
        try {
            // Check if email already exists
            const existingSubscriber = await this.databases.listDocuments(
                conf.appwriteDatabaseId,
                conf.appwriteNewsletterCollectionId,
                [Query.equal("email", email)]
            );

            if (existingSubscriber.documents.length > 0) {
                throw new Error("This email is already subscribed to our newsletter!");
            }

            // Create new subscription
            const subscription = await this.databases.createDocument(
                conf.appwriteDatabaseId,
                conf.appwriteNewsletterCollectionId,
                ID.unique(),
                {
                    email,
                    subscribedAt: new Date().toISOString(),
                    isActive: true
                }
            );

            // Send confirmation email
            await this.sendConfirmationEmail(email);

            return subscription;
        } catch (error) {
            console.error("NewsletterService :: subscribeToNewsletter :: error", error);
            throw error;
        }
    }

    async sendConfirmationEmail(email) {
        try {
            const templateParams = {
                to_email: email,
                to_name: email.split('@')[0], // Use part before @ as name
                from_name: "TechieBlog Team",
                message: `Welcome to TechieBlog! 🎉

Thank you for subscribing to our newsletter. You're now part of our community of tech enthusiasts!

Here's what you can expect:
• Weekly deep dives into cutting-edge technology
• Exclusive content and insights
• Early access to new articles
• Zero spam - we respect your inbox

Stay tuned for amazing content coming your way!

Best regards,
The TechieBlog Team

---
If you didn't subscribe to this newsletter, please ignore this email.`
            };

            const result = await emailjs.send(
                import.meta.env.VITE_EMAILJS_SERVICE_ID_NEWSLETTER,
                import.meta.env.VITE_EMAILJS_TEMPLATE_ID_NEWSLETTER,
                templateParams,
                import.meta.env.VITE_EMAILJS_PUBLIC_KEY_NEWSLETTER
            );

            console.log('Newsletter confirmation email sent successfully:', result.text);
            return result;
        } catch (error) {
            console.error('Error sending newsletter confirmation email:', error);
            throw new Error('Failed to send confirmation email. Please try again.');
        }
    }

    async unsubscribe(email) {
        try {
            const subscriber = await this.databases.listDocuments(
                conf.appwriteDatabaseId,
                conf.appwriteNewsletterCollectionId,
                [Query.equal("email", email)]
            );

            if (subscriber.documents.length === 0) {
                throw new Error("Email not found in our newsletter list.");
            }

            // Update subscription status instead of deleting
            const updatedSubscription = await this.databases.updateDocument(
                conf.appwriteDatabaseId,
                conf.appwriteNewsletterCollectionId,
                subscriber.documents[0].$id,
                {
                    isActive: false,
                    unsubscribedAt: new Date().toISOString()
                }
            );

            return updatedSubscription;
        } catch (error) {
            console.error("NewsletterService :: unsubscribe :: error", error);
            throw error;
        }
    }

    async getSubscribers() {
        try {
            const response = await this.databases.listDocuments(
                conf.appwriteDatabaseId,
                conf.appwriteNewsletterCollectionId,
                [Query.equal("isActive", true)]
            );

            return response.documents;
        } catch (error) {
            console.error("NewsletterService :: getSubscribers :: error", error);
            return [];
        }
    }
}

const newsletterService = new NewsletterService();
export default newsletterService;
